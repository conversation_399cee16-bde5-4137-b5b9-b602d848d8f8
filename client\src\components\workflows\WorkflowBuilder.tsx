import React, { useState, useCallback, useRef, useEffect } from 'react';
import ReactFlow, {
  ReactFlowProvider,
  Background,
  Controls,
  ConnectionLineType,
  ConnectionMode,
  Node,
  Connection,
  addEdge,
  useNodesState,
  useEdgesState,
  Panel,
  BackgroundVariant,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useIsMobile } from '@/hooks/use-mobile';

import { Workflow, NodeData, NodeType } from '@/types/workflow';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { useWorkflowExecution } from '@/hooks/useWorkflowExecution';

// Import custom nodes
import InputNode from './nodes/InputNode';
import PromptNode from './nodes/PromptNode';
import AgentNode from './nodes/AgentNode';
import ApiNode from './nodes/ApiNode';
import ApiTriggerNode from './nodes/ApiTriggerNode';
import CustomNode from './nodes/CustomNode';
import NodePanel from './NodePanel';
import NodeProperties from './NodeProperties';
import NodeExecutionView from './NodeExecutionView';
import NodeConfigModal from './modals/NodeConfigModal';
import RunStatusModal from './modals/RunStatusModal';
import RunWorkflowModal from './modals/RunWorkflowModal';
import { TransformNode } from './nodes/TransformNode';

// Register custom node types
const nodeTypes = {
  input: InputNode,
  prompt: PromptNode,
  agent: AgentNode,
  api: ApiNode,
  'api-trigger': ApiTriggerNode,
  custom: CustomNode,
  transform: TransformNode,
};

interface WorkflowBuilderProps {
  workflow: Workflow;
  onSave: (workflow: Partial<Workflow>) => void;
  onRun: () => void;
  layoutKey?: string; // Used to trigger resize when layout changes
  selectedRunId?: number; // Selected run to display execution data for
}

const WorkflowBuilder: React.FC<WorkflowBuilderProps> = ({ workflow, onSave, onRun, layoutKey, selectedRunId }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);
  const isMobile = useIsMobile();

  // State for mobile controls
  const [isNodePanelOpen, setIsNodePanelOpen] = useState(false);

  // Convert workflow data to React Flow format
  const initialNodes = Object.values(workflow.nodes).map(node => {
    // Ensure node type is one of the valid types
    const validType = (node.type && ['input', 'api', 'api-trigger', 'prompt', 'agent', 'custom'].includes(node.type))
      ? node.type as NodeType
      : 'input' as NodeType;

    return {
      id: node.id,
      type: validType,
      position: node.position,
      data: node.data,
    };
  });

  const initialEdges = Object.values(workflow.edges).map(edge => ({
    id: edge.id,
    source: edge.source,
    target: edge.target,
    type: 'smoothstep',
    animated: true,
  }));

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
  const [isRunModalOpen, setIsRunModalOpen] = useState(false);
  const [isRunStatusModalOpen, setIsRunStatusModalOpen] = useState(false);
  const [isExecutionViewOpen, setIsExecutionViewOpen] = useState(false);

  // Workflow execution state management
  const {
    executionState,
    getNodeExecutionState,
    isExecuting,
    startTracking,
    targetRun
  } = useWorkflowExecution({
    workflowId: workflow.id,
    selectedRunId
  });

  // Save workflow mutation
  const saveWorkflowMutation = useMutation({
    mutationFn: async (workflowData: Partial<Workflow>) => {
      return apiRequest('PATCH', `/api/workflows/${workflow.id}`, workflowData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
      queryClient.invalidateQueries({ queryKey: [`/api/workflows/${workflow.id}`] });
      toast({
        title: "Workflow saved",
        description: "Your workflow has been saved successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to save workflow",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Handle saving the workflow
  const handleSave = useCallback(() => {
    // Convert React Flow data back to workflow format
    const updatedNodes = nodes.reduce((acc, node) => {
      // Ensure node type is valid NodeType
      const validType = (node.type && ['input', 'api', 'api-trigger', 'prompt', 'agent', 'custom'].includes(node.type))
        ? node.type as NodeType
        : 'input' as NodeType;

      acc[node.id] = {
        id: node.id,
        type: validType,
        position: node.position,
        data: node.data,
      } as NodeData;
      return acc;
    }, {} as Record<string, NodeData>);

    const updatedEdges = edges.reduce((acc, edge) => {
      acc[edge.id] = {
        id: edge.id,
        source: edge.source,
        target: edge.target,
      };
      return acc;
    }, {} as Record<string, { id: string; source: string; target: string }>);

    const workflowData = {
      nodes: updatedNodes,
      edges: updatedEdges,
    };

    saveWorkflowMutation.mutate(workflowData);
    onSave(workflowData);
  }, [nodes, edges, workflow.id]);

  // Connection validation
  const isValidConnection = useCallback((connection: Connection) => {
    // Prevent self-connections
    if (connection.source === connection.target) {
      return false;
    }

    // Check if connection already exists
    const existingConnection = edges.find(
      edge => edge.source === connection.source && edge.target === connection.target
    );
    if (existingConnection) {
      return false;
    }

    // Prevent multiple inputs to the same target
    const existingTargetConnection = edges.find(
      edge => edge.target === connection.target && edge.targetHandle === connection.targetHandle
    );
    if (existingTargetConnection) {
      return false;
    }

    return true;
  }, [edges]);

  // Connection handlers
  const onConnect = useCallback((params: Connection) => {
    if (isValidConnection(params)) {
      setEdges((eds) => addEdge({
        ...params,
        type: 'smoothstep',
        animated: true,
        id: `e${params.source}-${params.target}`
      }, eds));
    }
  }, [setEdges, isValidConnection]);

  // Connection start handler for better UX
  const onConnectStart = useCallback((_: React.MouseEvent | React.TouchEvent, params: { nodeId: string | null; handleType: string | null }) => {
    if (params.nodeId && params.handleType) {
      console.log(`Starting connection from ${params.nodeId} (${params.handleType})`);
    }
  }, []);

  // Connection end handler
  const onConnectEnd = useCallback((_: MouseEvent | TouchEvent) => {
    console.log('Connection ended');
  }, []);

  // Node selection handler - now only opens properties panel
  const onNodeClick = useCallback((_: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    // Always open properties panel for normal node clicks
    setIsExecutionViewOpen(false);
  }, []);

  // Execution status click handler - opens execution view
  const onExecutionStatusClick = useCallback((node: Node) => {
    setSelectedNode(node);
    setIsExecutionViewOpen(true);
  }, []);

  // Function to get execution order of nodes (topological sort)
  const getExecutionOrder = useCallback((workflow: any): string[] => {
    if (!workflow?.nodes || !workflow?.edges) return [];

    const nodeIds = Object.keys(workflow.nodes);
    const edges = Object.values(workflow.edges) as any[];
    const visited = new Set<string>();
    const order: string[] = [];

    // Simple topological sort
    const visit = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      // Visit dependencies first
      const incomingEdges = edges.filter((edge: any) => edge.target === nodeId);
      for (const edge of incomingEdges) {
        visit(edge.source);
      }

      order.push(nodeId);
    };

    for (const nodeId of nodeIds) {
      visit(nodeId);
    }

    return order;
  }, []);

  // Get nodes sorted by execution order
  const getNodesInExecutionOrder = useCallback(() => {
    if (!workflow) return nodes;

    const executionOrder = getExecutionOrder(workflow);
    const nodeMap = new Map(nodes.map(node => [node.id, node]));

    // Return nodes sorted by execution order, with any missing nodes at the end
    const sortedNodes = executionOrder
      .map(nodeId => nodeMap.get(nodeId))
      .filter(Boolean) as Node[];

    // Add any nodes that weren't in the execution order (shouldn't happen, but safety)
    const executionOrderSet = new Set(executionOrder);
    const remainingNodes = nodes.filter(node => !executionOrderSet.has(node.id));

    return [...sortedNodes, ...remainingNodes];
  }, [nodes, workflow, getExecutionOrder]);

  // Navigation handler for node execution view
  const handleNavigateToNode = useCallback((nodeId: string) => {
    const targetNode = nodes.find(n => n.id === nodeId);
    if (targetNode) {
      setSelectedNode(targetNode);
      // Keep execution view open, just switch to the new node
    }
  }, [nodes]);

  // Node update handler
  const onNodeUpdate = useCallback((nodeId: string, data: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          return { ...node, data: { ...node.data, ...data } };
        }
        return node;
      })
    );
  }, [setNodes]);

  // Drop event handler for adding new nodes
  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      if (!reactFlowWrapper.current || !reactFlowInstance) return;

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      let nodeType = event.dataTransfer.getData('application/reactflow');
      const isFromNodePanel = event.dataTransfer.getData('application/node-panel') === 'true';

      // Only create new nodes if the drag originated from the node panel
      // This prevents creating nodes when dragging existing nodes or handles
      if (!isFromNodePanel || typeof nodeType === 'undefined' || !nodeType || nodeType === '') {
        return; // Don't create a node if not from node panel or no valid type is provided
      }

      // Validate node type
      const validNodeType = ['input', 'api', 'api-trigger', 'prompt', 'agent', 'custom'].includes(nodeType)
        ? nodeType as NodeType
        : null;

      if (!validNodeType) {
        return; // Don't create a node if type is invalid
      }

      // Calculate position relative to the ReactFlow viewport
      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      // Generate a unique ID
      const id = `${validNodeType}-${Date.now()}`;

      // Create node data based on type
      let newNode: Node = {
        id,
        type: validNodeType,
        position,
        data: { name: `New ${validNodeType.charAt(0).toUpperCase() + validNodeType.slice(1)} Node` },
      };

      // Add the new node to the workflow
      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  // Run workflow handler
  const handleRunWorkflow = useCallback(() => {
    setIsRunModalOpen(true);
  }, []);

  // Handle run completion
  const handleRunComplete = useCallback((workflowRunId?: number) => {
    setIsRunModalOpen(false);
    if (workflowRunId) {
      startTracking(workflowRunId);
    }
    setIsRunStatusModalOpen(true);
    onRun();
  }, [onRun, startTracking]);

  // Update nodes from workflow when it changes
  useEffect(() => {
    if (workflow && workflow.nodes) {
      const newNodes = Object.values(workflow.nodes).map(node => {
        const nodeExecutionState = getNodeExecutionState(node.id);
        return {
          id: node.id,
          type: node.type as NodeType,
          position: node.position,
          data: {
            ...node.data,
            executionStatus: nodeExecutionState.status,
            onExecutionStatusClick: () => onExecutionStatusClick({
              id: node.id,
              type: node.type as NodeType,
              position: node.position,
              data: node.data
            } as Node)
          },
        };
      });

      const newEdges = Object.values(workflow.edges).map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: 'smoothstep',
        animated: true,
      }));

      setNodes(newNodes);
      setEdges(newEdges);
    }
  }, [workflow, getNodeExecutionState, onExecutionStatusClick]);

  // Update nodes when execution state changes
  useEffect(() => {
    if (!getNodeExecutionState || !executionState) return;

    setNodes(currentNodes =>
      currentNodes.map(node => {
        try {
          const nodeExecutionState = getNodeExecutionState(node.id);
          return {
            ...node,
            data: {
              ...node.data,
              executionStatus: nodeExecutionState?.status || 'idle',
              onExecutionStatusClick: () => onExecutionStatusClick(node)
            }
          };
        } catch (error) {
          console.warn('Error updating node execution state:', error);
          return node;
        }
      })
    );
  }, [executionState, getNodeExecutionState, onExecutionStatusClick]);

  // Trigger React Flow resize when container dimensions change
  useEffect(() => {
    if (reactFlowInstance) {
      // Small delay to ensure layout has settled
      const timer = setTimeout(() => {
        reactFlowInstance.fitView();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [reactFlowInstance, layoutKey]);

  return (
    <div className="w-full h-full flex overflow-hidden">
      <ReactFlowProvider>
        <div ref={reactFlowWrapper} className="flex-1 h-full relative">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onConnectStart={onConnectStart}
            onConnectEnd={onConnectEnd}
            onNodeClick={onNodeClick}
            onInit={setReactFlowInstance as any}
            nodeTypes={nodeTypes}
            connectionLineType={ConnectionLineType.SmoothStep}
            onDrop={onDrop}
            onDragOver={onDragOver}
            isValidConnection={isValidConnection}
            fitView
            minZoom={0.2}
            maxZoom={4}
            defaultViewport={{ x: 0, y: 0, zoom: isMobile ? 0.6 : 1 }}
            zoomOnScroll={!isMobile}
            panOnScroll={isMobile}
            selectionOnDrag={!isMobile}
            zoomOnDoubleClick={!isMobile}
            panOnDrag={!isMobile}
            elementsSelectable={true}
            snapToGrid={!isMobile}
            preventScrolling={false}
            nodesDraggable={true}
            nodesConnectable={true}
            nodesFocusable={true}
            edgesFocusable={true}
            draggable={true}
            connectOnClick={false}
            deleteKeyCode={['Backspace', 'Delete']}
            connectionRadius={20}
            connectionMode={ConnectionMode.Loose}
            selectNodesOnDrag={false}
          >
            <Background
              color="#d9d9d9"
              variant={BackgroundVariant.Dots}
              gap={16}
              size={1}
            />
            <Controls showInteractive={!isMobile} />

            {/* Mobile Node Panel Toggle Button */}
            {isMobile && (
              <Panel position="top-left" className="bg-white dark:bg-neutral-800 p-2 rounded shadow-md z-50 m-2">
                <button
                  onClick={() => setIsNodePanelOpen(!isNodePanelOpen)}
                  className="flex items-center text-sm px-3 py-1.5 bg-primary text-white rounded"
                >
                  {isNodePanelOpen ? 'Hide Nodes' : 'Add Node'}
                </button>
              </Panel>
            )}

            <Panel position="top-right" className="bg-white dark:bg-neutral-800 p-2 rounded shadow-md m-2">
              <div className="flex flex-col space-y-2">
                <button
                  onClick={handleSave}
                  disabled={saveWorkflowMutation.isPending}
                  className="flex items-center text-sm px-3 py-1.5 bg-primary text-white rounded hover:bg-primary/90 disabled:opacity-50"
                >
                  {saveWorkflowMutation.isPending ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    isMobile ? 'Save' : 'Save Workflow'
                  )}
                </button>

                {isMobile && (
                  <button
                    onClick={handleRunWorkflow}
                    disabled={isExecuting}
                    className="flex items-center text-sm px-3 py-1.5 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                  >
                    {isExecuting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Running...
                      </>
                    ) : (
                      'Run'
                    )}
                  </button>
                )}

                {/* Execution Status Indicator */}
                {executionState.status !== 'idle' && (
                  <div className="text-xs text-center p-2 bg-neutral-50 dark:bg-neutral-900 rounded">
                    <div className="font-medium capitalize">{executionState.status}</div>
                    {executionState.workflowRunId && (
                      <div className="text-neutral-500">Run #{executionState.workflowRunId}</div>
                    )}
                  </div>
                )}
              </div>
            </Panel>
          </ReactFlow>
        </div>

        <NodePanel />

        {selectedNode && !isExecutionViewOpen && (
          <NodeProperties
            node={selectedNode}
            onUpdate={onNodeUpdate}
            onClose={() => {
              setSelectedNode(null);
              setIsExecutionViewOpen(false);
            }}
            onConfigure={() => setIsConfigModalOpen(true)}
          />
        )}
      </ReactFlowProvider>

      {isConfigModalOpen && selectedNode && (
        <NodeConfigModal
          node={selectedNode}
          onClose={() => setIsConfigModalOpen(false)}
          onUpdate={onNodeUpdate}
        />
      )}

      {isRunModalOpen && (
        <RunWorkflowModal
          workflow={workflow}
          onClose={() => setIsRunModalOpen(false)}
          onRunComplete={handleRunComplete}
        />
      )}

      {isRunStatusModalOpen && (
        <RunStatusModal
          workflow={workflow}
          onClose={() => setIsRunStatusModalOpen(false)}
        />
      )}

      {isExecutionViewOpen && selectedNode && (
        <NodeExecutionView
          node={selectedNode}
          executionState={getNodeExecutionState(selectedNode.id)}
          workflowRunId={executionState.workflowRunId}
          allNodes={getNodesInExecutionOrder()}
          onNavigateToNode={handleNavigateToNode}
          onClose={() => {
            setIsExecutionViewOpen(false);
            setSelectedNode(null);
          }}
          onConfigure={() => {
            setIsExecutionViewOpen(false);
            setIsConfigModalOpen(true);
          }}
        />
      )}
    </div>
  );
};

export default WorkflowBuilder;
