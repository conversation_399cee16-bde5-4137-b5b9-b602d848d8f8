import React, { useState } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON>, Settings, PlayCircle, Star, Penci<PERSON>, <PERSON>, <PERSON>, Brain } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

interface TopNavigationProps {
  workflowName: string;
  workflowStatus: 'draft' | 'published';
  isFavorite: boolean;
  onRunWorkflow: () => void;
  onToggleFavorite: () => void;
  onToggleHistory?: () => void;
  isHistoryOpen?: boolean;
  workflowId?: number;
  onAnalyzeWorkflow?: () => void;
}

const TopNavigation: React.FC<TopNavigationProps> = ({
  workflowName,
  workflowStatus,
  isFavorite,
  onRunWorkflow,
  onToggleFavorite,
  onToggleHistory,
  isHistoryOpen,
  workflowId,
  onAnalyzeWorkflow
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(workflowName);

  // Update workflow name mutation
  const updateWorkflowMutation = useMutation({
    mutationFn: async () => {
      if (!workflowId) throw new Error('Workflow ID is required');
      return apiRequest('PATCH', `/api/workflows/${workflowId}`, {
        name: editedName
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
      queryClient.invalidateQueries({ queryKey: [`/api/workflows/${workflowId}`] });
      setIsEditing(false);
      toast({
        title: "Workflow updated",
        description: "The workflow name has been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update workflow",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleRunWorkflow = () => {
    onRunWorkflow();
  };

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editedName.trim()) {
      updateWorkflowMutation.mutate();
    }
  };

  return (
    <header className="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 h-14 flex items-center px-4 justify-between">
      <div className="flex items-center">
        {isEditing ? (
          <form onSubmit={handleNameSubmit} className="flex items-center gap-2">
            <Input
              value={editedName}
              onChange={(e) => setEditedName(e.target.value)}
              className="h-8 w-64"
              autoFocus
            />
            <Button type="submit" size="sm" variant="ghost">
              <Check className="w-4 h-4" />
            </Button>
            <Button
              type="button"
              size="sm"
              variant="ghost"
              onClick={() => {
                setIsEditing(false);
                setEditedName(workflowName);
              }}
            >
              <X className="w-4 h-4" />
            </Button>
          </form>
        ) : (
          <div className="flex items-center gap-2">
            <h2 className="font-semibold text-neutral-800 dark:text-white">{workflowName}</h2>
            <button
              onClick={() => setIsEditing(true)}
              className="text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300"
            >
              <Pencil className="w-4 h-4" />
            </button>
          </div>
        )}
        <div className="ml-4 flex items-center space-x-2">
          <span className={`px-2 py-0.5 text-xs rounded-full ${
            workflowStatus === 'published'
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
          }`}>
            {workflowStatus === 'published' ? 'Published' : 'Draft'}
          </span>
          <button
            className={`text-${isFavorite ? 'yellow' : 'neutral'}-500 hover:text-yellow-500`}
            title={isFavorite ? "Remove from favorites" : "Add to favorites"}
            onClick={onToggleFavorite}
          >
            <Star className={`w-4 h-4 ${isFavorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />
          </button>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        {onToggleHistory ? (
          <Button
            variant={isHistoryOpen ? "default" : "outline"}
            size="sm"
            className="text-sm"
            onClick={onToggleHistory}
          >
            <History className="w-4 h-4 mr-1.5" />
            History
          </Button>
        ) : (
          <Link href="/run-history">
            <Button variant="outline" size="sm" className="text-sm">
              <History className="w-4 h-4 mr-1.5" />
              History
            </Button>
          </Link>
        )}
        {onAnalyzeWorkflow && (
          <Button
            variant="outline"
            size="sm"
            className="text-sm"
            onClick={onAnalyzeWorkflow}
          >
            <Brain className="w-4 h-4 mr-1.5" />
            AI Analysis
          </Button>
        )}
        <Link href="/settings">
          <Button variant="outline" size="sm" className="text-sm">
            <Settings className="w-4 h-4 mr-1.5" />
            Settings
          </Button>
        </Link>
        <Button onClick={handleRunWorkflow} className="text-sm bg-primary hover:bg-primary/90">
          <PlayCircle className="w-4 h-4 mr-1.5" />
          Run Workflow
        </Button>
      </div>
    </header>
  );
};

export default TopNavigation;
