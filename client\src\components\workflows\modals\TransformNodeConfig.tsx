import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { TransformNodeData } from '../../../types/workflow';
import SchemaBuilder from '@/components/workflows/SchemaBuilder';
import CodeEditor from '@/components/ui/code-editor';

interface TransformNodeConfigProps {
  node: TransformNodeData;
  onUpdate: (node: TransformNodeData) => void;
}

// Conversion helpers
function transformSchemaToInputSchema(schema: any, title = '', description = ''): any {
  // If already InputSchema, return as is
  if (schema && schema.version && Array.isArray(schema.fields)) return schema;
  const fields = schema?.properties
    ? Object.entries(schema.properties).map(([name, prop]: [string, any], idx) => ({
        id: `field_${idx}`,
        name,
        label: name.charAt(0).toUpperCase() + name.slice(1),
        type: prop.type || 'string',
        description: prop.description || '',
        validation: { required: prop.required || false },
      }))
    : [];
  return {
    version: '1.0',
    title,
    description,
    fields,
  };
}

function inputSchemaToTransformSchema(inputSchema: any): any {
  const properties: Record<string, any> = {};
  (inputSchema.fields || []).forEach((field: any) => {
    properties[field.name] = {
      type: field.type,
      required: field.validation?.required || false,
      description: field.description || '',
    };
  });
  return {
    type: 'object',
    properties,
  };
}

export const TransformNodeConfig: React.FC<TransformNodeConfigProps> = ({ node, onUpdate }) => {
  const [activeTab, setActiveTab] = useState<string>('basic');

  const handleBasicInfoChange = (field: string, value: string) => {
    onUpdate({
      ...node,
      data: {
        ...node.data,
        [field]: value,
      },
    });
  };

  const handleTransformationTypeChange = (type: 'javascript' | 'template' | 'mapping') => {
    onUpdate({
      ...node,
      data: {
        ...node.data,
        transformation: {
          type,
          code: type === 'javascript' ? node.data.transformation.code : undefined,
          template: type === 'template' ? node.data.transformation.template : undefined,
          mapping: type === 'mapping' ? node.data.transformation.mapping : undefined,
        },
      },
    });
  };

  const handleSchemaChange = (schema: any, type: 'input' | 'output') => {
    onUpdate({
      ...node,
      data: {
        ...node.data,
        [`${type}Schema`]: schema,
      },
    });
  };

  const handleTransformationChange = (value: string) => {
    const transformation = { ...node.data.transformation };
    if (transformation.type === 'javascript') {
      transformation.code = value;
    } else if (transformation.type === 'template') {
      transformation.template = value;
    }
    onUpdate({
      ...node,
      data: {
        ...node.data,
        transformation,
      },
    });
  };

  return (
    <div className="w-full p-4">
      <h2 className="text-lg font-semibold mb-2">Transform Node Configuration</h2>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
        <TabsList className="grid grid-cols-4 w-full mb-2">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="schema">Schema</TabsTrigger>
          <TabsTrigger value="transformation">Transformation</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        <TabsContent value="basic">
          <div className="space-y-4">
            <div>
              <Label htmlFor="transform-name">Name</Label>
              <Input
                id="transform-name"
                value={node.data.name}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleBasicInfoChange('name', e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="transform-description">Description</Label>
              <Textarea
                id="transform-description"
                value={node.data.description || ''}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleBasicInfoChange('description', e.target.value)}
                className="mt-1"
                rows={2}
              />
            </div>
            <div>
              <Label>Transformation Type</Label>
              <Select
                value={node.data.transformation.type}
                onValueChange={(value: string) => handleTransformationTypeChange(value as 'javascript' | 'template' | 'mapping')}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="template">Template</SelectItem>
                  <SelectItem value="mapping">Mapping</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="schema">
          <div className="space-y-4">
            <div>
              <Label>Input Schema</Label>
              <SchemaBuilder
                schema={transformSchemaToInputSchema(node.data.inputSchema, 'Input Schema', 'Input for this transform node')}
                onChange={(schema: any) => handleSchemaChange(inputSchemaToTransformSchema(schema), 'input')}
              />
            </div>
            <div>
              <Label>Output Schema</Label>
              <SchemaBuilder
                schema={transformSchemaToInputSchema(node.data.outputSchema, 'Output Schema', 'Output for this transform node')}
                onChange={(schema: any) => handleSchemaChange(inputSchemaToTransformSchema(schema), 'output')}
              />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="transformation">
          <div className="space-y-4">
            {node.data.transformation.type === 'javascript' && (
              <div>
                <Label>JavaScript Code</Label>
                <CodeEditor
                  value={node.data.transformation.code || ''}
                  onChange={handleTransformationChange}
                  language="javascript"
                  height="300px"
                />
              </div>
            )}
            {node.data.transformation.type === 'template' && (
              <div>
                <Label>Template</Label>
                <Textarea
                  value={node.data.transformation.template || ''}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleTransformationChange(e.target.value)}
                  className="mt-1 font-mono"
                  rows={10}
                />
              </div>
            )}
            {node.data.transformation.type === 'mapping' && (
              <div>
                <Label>Mapping configuration will be implemented here</Label>
                <div className="text-xs text-neutral-500">(Coming soon)</div>
              </div>
            )}
          </div>
        </TabsContent>
        <TabsContent value="settings">
          <div className="space-y-4">
            <div>
              <Label>Timeout (ms)</Label>
              <Input
                type="number"
                value={node.data.executionSettings.timeout}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onUpdate({
                    ...node,
                    data: {
                      ...node.data,
                      executionSettings: {
                        ...node.data.executionSettings,
                        timeout: parseInt(e.target.value) || 0,
                      },
                    },
                  })
                }
                className="mt-1"
              />
            </div>
            <div>
              <Label>Retry Count</Label>
              <Input
                type="number"
                value={node.data.executionSettings.retryCount}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onUpdate({
                    ...node,
                    data: {
                      ...node.data,
                      executionSettings: {
                        ...node.data.executionSettings,
                        retryCount: parseInt(e.target.value) || 0,
                      },
                    },
                  })
                }
                className="mt-1"
              />
            </div>
            <div>
              <Label>Retry Delay (ms)</Label>
              <Input
                type="number"
                value={node.data.executionSettings.retryDelay}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  onUpdate({
                    ...node,
                    data: {
                      ...node.data,
                      executionSettings: {
                        ...node.data.executionSettings,
                        retryDelay: parseInt(e.target.value) || 0,
                      },
                    },
                  })
                }
                className="mt-1"
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 