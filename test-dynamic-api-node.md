# Dynamic API Node Test Guide

This guide demonstrates how to test the new dynamic parameters and data transformation features in API nodes.

## Test Scenario 1: Dynamic URL with Path Parameters

### Setup
1. Create a workflow with an API Trigger node and an API node
2. Configure the API node with:
   - URL: `https://jsonplaceholder.typicode.com/users/{{user_id}}/posts/{{post_id}}`
   - Method: GET
   - Enable Dynamic Parameters
   - Path Parameters:
     ```json
     {
       "user_id": "{{body.userId}}",
       "post_id": "{{body.postId}}"
     }
     ```

### Test Input
Send a POST request to the API trigger with:
```json
{
  "userId": "1",
  "postId": "1"
}
```

### Expected Result
The API node should make a GET request to: `https://jsonplaceholder.typicode.com/users/1/posts/1`

## Test Scenario 2: Dynamic Query Parameters

### Setup
1. Configure an API node with:
   - URL: `https://jsonplaceholder.typicode.com/posts`
   - Method: GET
   - Enable Dynamic Parameters
   - Query Parameters:
     ```json
     {
       "userId": "{{user.id}}",
       "limit": "{{pagination.limit}}",
       "_sort": "id",
       "_order": "desc"
     }
     ```

### Test Input
```json
{
  "user": {
    "id": "2"
  },
  "pagination": {
    "limit": "5"
  }
}
```

### Expected Result
The API node should make a GET request to: `https://jsonplaceholder.typicode.com/posts?userId=2&limit=5&_sort=id&_order=desc`

## Test Scenario 3: Dynamic Request Body

### Setup
1. Configure an API node with:
   - URL: `https://jsonplaceholder.typicode.com/posts`
   - Method: POST
   - Enable Dynamic Parameters
   - Request Body Template:
     ```json
     {
       "title": "{{post.title}}",
       "body": "{{post.content}}",
       "userId": "{{user.id}}",
       "metadata": {
         "timestamp": "{{timestamp}}",
         "source": "workflow"
       }
     }
     ```

### Test Input
```json
{
  "post": {
    "title": "My Dynamic Post",
    "content": "This post was created using dynamic parameters"
  },
  "user": {
    "id": "1"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Expected Result
The API node should send a POST request with the transformed body.

## Test Scenario 4: Data Transformation

### Setup
1. Configure an API node with:
   - URL: `https://jsonplaceholder.typicode.com/users`
   - Method: GET
   - Enable Data Transformation
   - Input Mapping:
     ```json
     {
       "search_term": "name",
       "filter_active": "status"
     }
     ```
   - Output Mapping:
     ```json
     {
       "users": "data",
       "total_count": "data.length",
       "first_user_name": "data.0.name"
     }
     ```

### Test Input
```json
{
  "name": "Leanne Graham",
  "status": "active"
}
```

### Expected Result
The output should include the mapped fields with the API response data.

## Test Scenario 5: Custom Transform Script

### Setup
1. Configure an API node with:
   - Enable Data Transformation
   - Custom Transform Script:
     ```javascript
     function transform(input, context) {
       return {
         ...input,
         timestamp: Date.now(),
         workflowId: context.workflowId,
         processedAt: new Date().toISOString(),
         enrichedData: {
           userAgent: input.headers?.['user-agent'] || 'unknown',
           ipAddress: input.headers?.['x-forwarded-for'] || 'unknown'
         }
       };
     }
     ```

### Test Input
```json
{
  "name": "Test User",
  "headers": {
    "user-agent": "Mozilla/5.0",
    "x-forwarded-for": "***********"
  }
}
```

### Expected Result
The input should be enriched with timestamp, workflow context, and extracted header information.

## Visual Indicators

When dynamic parameters or data transformation are enabled, the API node should display:
- Purple "Dynamic" indicator with lightning bolt icon
- Indigo "Transform" indicator with arrow icon
- These appear in a separate section above security features

## Testing Tips

1. Use the browser's Network tab to verify the actual HTTP requests being made
2. Check the workflow execution logs for detailed information about parameter processing
3. Test with missing variables to ensure graceful handling
4. Verify that template variables like `{{json.field}}` work correctly
5. Test complex nested object access like `{{user.profile.settings.theme}}`

## Common Template Variables

- `{{variable_name}}` - Simple variable replacement
- `{{object.property}}` - Nested object access
- `{{json.field}}` - JSON helper for complex objects
- `{{webhook.method}}` - Webhook-specific helpers
- `{{webhook.body_json}}` - Formatted webhook body

## Error Handling

The system should gracefully handle:
- Missing template variables (shows `[variable: not found]`)
- Invalid JSON in configuration
- Network errors in API requests
- Transformation script errors
