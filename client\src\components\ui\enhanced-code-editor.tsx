import { useRef, useEffect, useState } from "react";
import monaco from "@/lib/monaco-setup";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Play,
  Save,
  Settings,
  FileText,
  Zap,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { JSCodeTemplate } from "@/types/workflow";

interface EnhancedCodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  height?: string;
  className?: string;
  onTest?: () => void;
  onSave?: () => void;
  templates?: JSCodeTemplate[];
  executionStatus?: 'idle' | 'running' | 'success' | 'error';
  executionTime?: number;
  errors?: string[];
}

// TypeScript definitions for the enhanced context
const CONTEXT_TYPES = `
interface ExecutionContext {
  workflowId: number;
  runId: number;
  nodeId: string;
  nodeName: string;
  executionTime: Date;
  nodeOutputs: Record<string, any>;

  utils: {
    fetch: (url: string, options?: RequestInit) => Promise<Response>;
    log: (level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: any) => void;
    sleep: (ms: number) => Promise<void>;
    crypto: {
      hash: (data: string, algorithm?: 'sha256' | 'md5') => string;
      encrypt: (data: string, key: string) => string;
      decrypt: (data: string, key: string) => string;
      uuid: () => string;
    };
    date: {
      format: (date: Date, format: string) => string;
      parse: (dateString: string, format?: string) => Date;
      add: (date: Date, amount: number, unit: 'days' | 'hours' | 'minutes') => Date;
      diff: (date1: Date, date2: Date, unit: 'days' | 'hours' | 'minutes') => number;
    };
    json: {
      parse: (jsonString: string) => any;
      stringify: (obj: any, pretty?: boolean) => string;
      validate: (jsonString: string) => boolean;
      transform: (obj: any, transformer: (key: string, value: any) => any) => any;
    };
    string: {
      slugify: (text: string) => string;
      capitalize: (text: string) => string;
      truncate: (text: string, length: number) => string;
      template: (template: string, variables: Record<string, any>) => string;
    };
    array: {
      chunk: <T>(array: T[], size: number) => T[][];
      unique: <T>(array: T[]) => T[];
      groupBy: <T>(array: T[], key: string) => Record<string, T[]>;
      sortBy: <T>(array: T[], key: string, order?: 'asc' | 'desc') => T[];
    };
  };

  env: Record<string, string>;

  secrets: {
    get: (key: string) => Promise<string>;
    set: (key: string, value: string) => Promise<void>;
  };
}

declare function execute(input: any, context: ExecutionContext): any | Promise<any>;
`;

export function EnhancedCodeEditor({
  value,
  onChange,
  language = "javascript",
  height = "400px",
  className = "",
  onTest,
  onSave,
  templates = [],
  executionStatus = 'idle',
  executionTime,
  errors = []
}: EnhancedCodeEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoEditorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);

  useEffect(() => {
    if (editorRef.current && !monacoEditorRef.current) {
      try {
        // Add TypeScript definitions for better IntelliSense
        monaco.languages.typescript.javascriptDefaults.addExtraLib(
          CONTEXT_TYPES,
          'ts:context.d.ts'
        );

        // Configure JavaScript/TypeScript compiler options
        monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
          target: monaco.languages.typescript.ScriptTarget.ES2020,
          allowNonTsExtensions: true,
          moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
          module: monaco.languages.typescript.ModuleKind.CommonJS,
          noEmit: true,
          esModuleInterop: true,
          jsx: monaco.languages.typescript.JsxEmit.React,
          reactNamespace: "React",
          allowJs: true,
          typeRoots: ["node_modules/@types"]
        });

        monacoEditorRef.current = monaco.editor.create(editorRef.current, {
          value,
          language,
          theme: "custom-dark",
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          fontSize: 14,
          lineNumbers: "on",
          folding: true,
          glyphMargin: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3,
          wordWrap: "on",
          contextmenu: true,
          quickSuggestions: {
            other: true,
            comments: false,
            strings: false
          },
          parameterHints: { enabled: true },
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnEnter: "on",
          tabCompletion: "on",
          wordBasedSuggestions: "currentDocument",
          suggest: {
            showKeywords: true,
            showSnippets: true,
            showFunctions: true,
            showConstructors: true,
            showFields: true,
            showVariables: true,
            showClasses: true,
            showStructs: true,
            showInterfaces: true,
            showModules: true,
            showProperties: true,
            showEvents: true,
            showOperators: true,
            showUnits: true,
            showValues: true,
            showConstants: true,
            showEnums: true,
            showEnumMembers: true,
            showTypeParameters: true,
            showIssues: true,
            showUsers: true,
            showColors: true,
            showFiles: true,
            showReferences: true,
            showFolders: true
          }
        });

        // Add custom code snippets
        monaco.languages.registerCompletionItemProvider('javascript', {
          provideCompletionItems: (model, position) => {
            const word = model.getWordUntilPosition(position);
            const range = {
              startLineNumber: position.lineNumber,
              endLineNumber: position.lineNumber,
              startColumn: word.startColumn,
              endColumn: word.endColumn
            };

            const suggestions = [
              {
                label: 'execute-function',
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: [
                  'async function execute(input, context) {',
                  '  // Your custom logic here',
                  '  context.utils.log("info", "Processing input", input);',
                  '  ',
                  '  return {',
                  '    result: "success",',
                  '    data: input',
                  '  };',
                  '}'
                ].join('\n'),
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: 'Basic execute function template',
                range
              },
              {
                label: 'http-request',
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: [
                  'const response = await context.utils.fetch("${1:url}", {',
                  '  method: "${2:GET}",',
                  '  headers: {',
                  '    "Content-Type": "application/json"',
                  '  },',
                  '  body: JSON.stringify(${3:data})',
                  '});',
                  'const result = await response.json();'
                ].join('\n'),
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: 'HTTP request template',
                range
              },
              {
                label: 'data-transform',
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: [
                  'const transformed = input.${1:items}.map(item => ({',
                  '  id: item.id,',
                  '  name: item.name,',
                  '  processed: true,',
                  '  timestamp: new Date().toISOString()',
                  '}));'
                ].join('\n'),
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: 'Data transformation template',
                range
              }
            ];
            return { suggestions };
          }
        });

        // Listen for changes in the editor
        const disposable = monacoEditorRef.current.onDidChangeModelContent(() => {
          onChange(monacoEditorRef.current?.getValue() || "");
        });

        // Store disposable for cleanup
        (monacoEditorRef.current as any)._changeDisposable = disposable;
      } catch (error) {
        console.error("Failed to create Monaco editor:", error);
      }
    }

    return () => {
      if (monacoEditorRef.current) {
        if ((monacoEditorRef.current as any)._changeDisposable) {
          (monacoEditorRef.current as any)._changeDisposable.dispose();
        }
        monacoEditorRef.current.dispose();
        monacoEditorRef.current = null;
      }
    };
  }, []);

  // Update editor content when value prop changes
  useEffect(() => {
    if (monacoEditorRef.current) {
      const currentValue = monacoEditorRef.current.getValue();
      if (value !== currentValue) {
        monacoEditorRef.current.setValue(value);
      }
    }
  }, [value]);

  const insertTemplate = (template: JSCodeTemplate) => {
    if (monacoEditorRef.current) {
      const position = monacoEditorRef.current.getPosition();
      monacoEditorRef.current.executeEdits('insert-template', [{
        range: new monaco.Range(
          position?.lineNumber || 1,
          position?.column || 1,
          position?.lineNumber || 1,
          position?.column || 1
        ),
        text: template.code
      }]);
      monacoEditorRef.current.focus();
    }
    setShowTemplates(false);
  };

  const formatCode = () => {
    if (monacoEditorRef.current) {
      monacoEditorRef.current.getAction('editor.action.formatDocument')?.run();
    }
  };

  const getStatusIcon = () => {
    switch (executionStatus) {
      case 'running':
        return <Zap className="w-4 h-4 animate-pulse text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className={`border rounded-md overflow-hidden ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center justify-between p-2 bg-neutral-100 dark:bg-neutral-800 border-b">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onTest}
            disabled={executionStatus === 'running'}
          >
            <Play className="w-4 h-4 mr-1" />
            Test
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onSave}
          >
            <Save className="w-4 h-4 mr-1" />
            Save
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatCode}
          >
            <FileText className="w-4 h-4 mr-1" />
            Format
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTemplates(!showTemplates)}
          >
            <Settings className="w-4 h-4 mr-1" />
            Templates
          </Button>
        </div>

        <div className="flex items-center gap-2">
          {getStatusIcon()}
          {executionTime && (
            <Badge variant="secondary" className="text-xs">
              {executionTime}ms
            </Badge>
          )}
          {errors.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              {errors.length} error{errors.length > 1 ? 's' : ''}
            </Badge>
          )}
        </div>
      </div>

      {/* Templates Panel */}
      {showTemplates && (
        <div className="p-2 bg-neutral-50 dark:bg-neutral-900 border-b max-h-32 overflow-y-auto">
          <div className="flex flex-wrap gap-1">
            {templates.map((template) => (
              <Button
                key={template.id}
                variant="outline"
                size="sm"
                onClick={() => insertTemplate(template)}
                className="text-xs"
              >
                {template.name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Editor */}
      <div style={{ height }}>
        <div ref={editorRef} className="w-full h-full" />
      </div>

      {/* Error Panel */}
      {errors.length > 0 && (
        <div className="p-2 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
          {errors.map((error, index) => (
            <div key={index} className="text-sm text-red-700 dark:text-red-300 font-mono">
              {error}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default EnhancedCodeEditor;
