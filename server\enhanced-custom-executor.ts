import { NodeExecutor, ExecutionContext } from './workflow-executor';
import { validate, ValidationError } from 'jsonschema';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import { storage } from './storage';

// Enhanced utility functions for custom JavaScript nodes
export class JSExecutionUtils {
  private context: ExecutionContext;
  private nodeId: string;
  private allowedDomains: string[];

  constructor(context: ExecutionContext, nodeId: string, allowedDomains: string[] = []) {
    this.context = context;
    this.nodeId = nodeId;
    this.allowedDomains = allowedDomains;
  }

  // HTTP client with domain restrictions
  async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    try {
      const urlObj = new URL(url);

      // Check if domain is allowed
      if (this.allowedDomains.length > 0) {
        const isAllowed = this.allowedDomains.some(domain =>
          urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
        );

        if (!isAllowed) {
          throw new Error(`Domain ${urlObj.hostname} is not in the allowed domains list`);
        }
      }

      // Add timeout if not specified
      const timeoutMs = 30000; // 30 seconds default
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

      try {
        const response = await globalThis.fetch(url, {
          ...options,
          signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    } catch (error) {
      await this.log('error', `HTTP request failed: ${error.message}`, { url, error: error.message });
      throw error;
    }
  }

  // Structured logging
  async log(level: 'info' | 'warn' | 'error' | 'debug', message: string, data?: any): Promise<void> {
    const logData = {
      nodeId: this.nodeId,
      level,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    switch (level) {
      case 'info':
        await this.context.logger.info(message, data);
        break;
      case 'warn':
        await this.context.logger.warn(message, data);
        break;
      case 'error':
        await this.context.logger.error(message, new Error(message), data);
        break;
      case 'debug':
        await this.context.logger.debug(message, data);
        break;
    }
  }

  // Sleep utility
  async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Crypto utilities
  get crypto() {
    return {
      hash: (data: string, algorithm: 'sha256' | 'md5' = 'sha256'): string => {
        return crypto.createHash(algorithm).update(data).digest('hex');
      },

      encrypt: (data: string, key: string): string => {
        const cipher = crypto.createCipher('aes-256-cbc', key);
        let encrypted = cipher.update(data, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
      },

      decrypt: (data: string, key: string): string => {
        const decipher = crypto.createDecipher('aes-256-cbc', key);
        let decrypted = decipher.update(data, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
      },

      uuid: (): string => {
        return uuidv4();
      }
    };
  }

  // Date utilities
  get date() {
    return {
      format: (date: Date, format: string): string => {
        // Simple date formatting - in production, use a library like date-fns
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return format
          .replace('YYYY', year.toString())
          .replace('MM', month)
          .replace('DD', day)
          .replace('HH', hours)
          .replace('mm', minutes)
          .replace('ss', seconds);
      },

      parse: (dateString: string, format?: string): Date => {
        return new Date(dateString);
      },

      add: (date: Date, amount: number, unit: 'days' | 'hours' | 'minutes'): Date => {
        const result = new Date(date);
        switch (unit) {
          case 'days':
            result.setDate(result.getDate() + amount);
            break;
          case 'hours':
            result.setHours(result.getHours() + amount);
            break;
          case 'minutes':
            result.setMinutes(result.getMinutes() + amount);
            break;
        }
        return result;
      },

      diff: (date1: Date, date2: Date, unit: 'days' | 'hours' | 'minutes'): number => {
        const diffMs = Math.abs(date1.getTime() - date2.getTime());
        switch (unit) {
          case 'days':
            return Math.floor(diffMs / (1000 * 60 * 60 * 24));
          case 'hours':
            return Math.floor(diffMs / (1000 * 60 * 60));
          case 'minutes':
            return Math.floor(diffMs / (1000 * 60));
          default:
            return diffMs;
        }
      }
    };
  }

  // JSON utilities
  get json() {
    return {
      parse: (jsonString: string): any => {
        try {
          return JSON.parse(jsonString);
        } catch (error) {
          throw new Error(`Invalid JSON: ${error.message}`);
        }
      },

      stringify: (obj: any, pretty: boolean = false): string => {
        return JSON.stringify(obj, null, pretty ? 2 : 0);
      },

      validate: (jsonString: string): boolean => {
        try {
          JSON.parse(jsonString);
          return true;
        } catch {
          return false;
        }
      },

      transform: (obj: any, transformer: (key: string, value: any) => any): any => {
        return JSON.parse(JSON.stringify(obj, transformer));
      }
    };
  }

  // String utilities
  get string() {
    return {
      slugify: (text: string): string => {
        return text
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/[\s_-]+/g, '-')
          .replace(/^-+|-+$/g, '');
      },

      capitalize: (text: string): string => {
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
      },

      truncate: (text: string, length: number): string => {
        return text.length > length ? text.substring(0, length) + '...' : text;
      },

      template: (template: string, variables: Record<string, any>): string => {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
          return variables[key] !== undefined ? String(variables[key]) : match;
        });
      }
    };
  }

  // Array utilities
  get array() {
    return {
      chunk: <T>(array: T[], size: number): T[][] => {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += size) {
          chunks.push(array.slice(i, i + size));
        }
        return chunks;
      },

      unique: <T>(array: T[]): T[] => {
        return [...new Set(array)];
      },

      groupBy: <T>(array: T[], key: string): Record<string, T[]> => {
        return array.reduce((groups, item) => {
          const groupKey = (item as any)[key];
          if (!groups[groupKey]) {
            groups[groupKey] = [];
          }
          groups[groupKey].push(item);
          return groups;
        }, {} as Record<string, T[]>);
      },

      sortBy: <T>(array: T[], key: string, order: 'asc' | 'desc' = 'asc'): T[] => {
        return [...array].sort((a, b) => {
          const aVal = (a as any)[key];
          const bVal = (b as any)[key];

          if (aVal < bVal) return order === 'asc' ? -1 : 1;
          if (aVal > bVal) return order === 'asc' ? 1 : -1;
          return 0;
        });
      }
    };
  }
}

// Enhanced Custom Node Executor
export class EnhancedCustomNodeExecutor implements NodeExecutor {
  async execute(nodeData: any, input: any, context: ExecutionContext, nodeId?: string): Promise<any> {
    const startTime = Date.now();

    try {
      // Get execution settings
      const settings = nodeData.executionSettings || {};
      const timeout = settings.timeout || 30000;
      const retryAttempts = settings.retryAttempts || 0;
      const enableLogging = settings.enableLogging !== false;
      const enableProfiling = settings.enableProfiling || false;

      // Validate input against schema if provided
      if (nodeData.inputSchema && nodeData.inputSchema.length > 0) {
        this.validateInput(input, nodeData.inputSchema);
      }

      // Create enhanced execution context
      const utils = new JSExecutionUtils(
        context,
        nodeId || 'unknown',
        nodeData.environment?.allowedDomains || []
      );

      const enhancedContext = {
        workflowId: context.workflow.id,
        runId: context.workflowRun.id,
        nodeId: nodeId || 'unknown',
        nodeName: nodeData.name || 'Custom Node',
        executionTime: new Date(),
        nodeOutputs: Object.fromEntries(context.nodeOutputs),
        utils,
        env: nodeData.environment?.variables || {},
        secrets: {
          get: async (key: string): Promise<string> => {
            // In production, implement secure secret retrieval
            return process.env[key] || '';
          },
          set: async (key: string, value: string): Promise<void> => {
            // In production, implement secure secret storage
            if (enableLogging) {
              await utils.log('info', `Secret ${key} updated`);
            }
          }
        }
      };

      // Execute with timeout and retry logic
      let lastError: Error | null = null;

      for (let attempt = 0; attempt <= retryAttempts; attempt++) {
        try {
          const result = await this.executeWithTimeout(
            nodeData.code,
            input,
            enhancedContext,
            timeout
          );

          // Validate output against schema if provided
          if (nodeData.outputSchema && nodeData.outputSchema.length > 0) {
            this.validateOutput(result, nodeData.outputSchema);
          }

          // Log performance metrics and record to database
          const executionTime = Date.now() - startTime;

          if (enableProfiling) {
            await utils.log('info', `Execution completed in ${executionTime}ms`, {
              attempt: attempt + 1,
              executionTime,
              memoryUsage: process.memoryUsage()
            });
          }

          // Record execution metrics
          try {
            const inputSize = JSON.stringify(input).length;
            const outputSize = JSON.stringify(result).length;
            const memoryUsage = process.memoryUsage();

            await storage.createNodeExecutionMetrics({
              nodeId: nodeId || 'unknown',
              workflowRunId: context.workflowRun.id,
              executionTimeMs: executionTime,
              memoryUsageMb: memoryUsage.heapUsed / 1024 / 1024,
              success: true,
              inputSizeBytes: inputSize,
              outputSizeBytes: outputSize,
              retryCount: attempt,
            });
          } catch (metricsError) {
            // Don't fail the execution if metrics recording fails
            console.warn('Failed to record execution metrics:', metricsError);
          }

          return result;

        } catch (error) {
          lastError = error as Error;

          if (attempt < retryAttempts) {
            if (enableLogging) {
              await utils.log('warn', `Execution attempt ${attempt + 1} failed, retrying...`, {
                error: error.message,
                attempt: attempt + 1,
                maxAttempts: retryAttempts + 1
              });
            }

            // Wait before retry (exponential backoff)
            await utils.sleep(Math.pow(2, attempt) * 1000);
          }
        }
      }

      throw lastError || new Error('Execution failed after all retry attempts');

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Record error metrics
      try {
        const inputSize = JSON.stringify(input).length;
        const memoryUsage = process.memoryUsage();

        await storage.createNodeExecutionMetrics({
          nodeId: nodeId || 'unknown',
          workflowRunId: context.workflowRun.id,
          executionTimeMs: executionTime,
          memoryUsageMb: memoryUsage.heapUsed / 1024 / 1024,
          success: false,
          errorMessage,
          inputSizeBytes: inputSize,
          outputSizeBytes: 0,
          retryCount: retryAttempts,
        });
      } catch (metricsError) {
        // Don't fail the execution if metrics recording fails
        console.warn('Failed to record error metrics:', metricsError);
      }

      await context.logger.error(`Enhanced custom node execution failed: ${errorMessage}`, error as Error, {
        nodeId,
        executionTime,
        nodeData: {
          name: nodeData.name,
          version: nodeData.version,
          settings: nodeData.executionSettings
        }
      });

      throw error;
    }
  }

  private async executeWithTimeout(
    code: string,
    input: any,
    context: any,
    timeoutMs: number
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Execution timed out after ${timeoutMs}ms`));
      }, timeoutMs);

      try {
        // Create a safe execution environment
        const customFunction = new Function('input', 'context', `
          "use strict";
          ${code}

          // If the code doesn't define an execute function, create a default one
          if (typeof execute !== 'function') {
            function execute(input, context) {
              return { result: 'Custom node executed', data: input };
            }
          }

          return execute(input, context);
        `);

        // Execute the custom function
        const result = customFunction(input, context);

        // Handle both sync and async results
        if (result && typeof result.then === 'function') {
          result
            .then((asyncResult: any) => {
              clearTimeout(timeout);
              resolve(asyncResult);
            })
            .catch((error: Error) => {
              clearTimeout(timeout);
              reject(error);
            });
        } else {
          clearTimeout(timeout);
          resolve(result);
        }

      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  private validateInput(input: any, schema: any[]): void {
    for (const param of schema) {
      if (param.required && (input[param.name] === undefined || input[param.name] === null)) {
        throw new Error(`Required parameter '${param.name}' is missing`);
      }

      if (input[param.name] !== undefined && param.validation) {
        this.validateParameterValue(input[param.name], param);
      }
    }
  }

  private validateOutput(output: any, schema: any[]): void {
    if (!output || typeof output !== 'object') {
      throw new Error('Output must be an object');
    }

    for (const param of schema) {
      if (output[param.name] === undefined) {
        throw new Error(`Expected output parameter '${param.name}' is missing`);
      }
    }
  }

  private validateParameterValue(value: any, param: any): void {
    const validation = param.validation;

    if (validation.min !== undefined && value < validation.min) {
      throw new Error(`Parameter '${param.name}' must be at least ${validation.min}`);
    }

    if (validation.max !== undefined && value > validation.max) {
      throw new Error(`Parameter '${param.name}' must be at most ${validation.max}`);
    }

    if (validation.minLength !== undefined && typeof value === 'string' && value.length < validation.minLength) {
      throw new Error(`Parameter '${param.name}' must be at least ${validation.minLength} characters long`);
    }

    if (validation.maxLength !== undefined && typeof value === 'string' && value.length > validation.maxLength) {
      throw new Error(`Parameter '${param.name}' must be at most ${validation.maxLength} characters long`);
    }

    if (validation.pattern && typeof value === 'string' && !new RegExp(validation.pattern).test(value)) {
      throw new Error(`Parameter '${param.name}' does not match the required pattern`);
    }

    if (validation.enum && !validation.enum.includes(value)) {
      throw new Error(`Parameter '${param.name}' must be one of: ${validation.enum.join(', ')}`);
    }
  }
}
