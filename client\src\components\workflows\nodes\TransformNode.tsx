import React from 'react';
import { Handle, Position } from 'reactflow';
import { TransformNodeData } from '../../../types/workflow';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

interface TransformNodeProps {
  data: TransformNodeData['data'];
  isConnectable: boolean;
}

export const TransformNode: React.FC<TransformNodeProps> = ({ data, isConnectable }) => {
  return (
    <Card className="min-w-[200px] border-2 border-cyan-500 rounded-lg shadow-md">
      <CardContent className="p-4">
        <div className="flex flex-col gap-1">
          <div className="text-base font-semibold text-cyan-700 truncate">
            {data.name}
          </div>
          {data.description && (
            <div className="text-xs text-neutral-500 truncate mb-1">
              {data.description}
            </div>
          )}
          <div className="mt-1">
            <Label className="text-xs text-cyan-700">Type: {data.transformation.type}</Label>
          </div>
        </div>
      </CardContent>
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        style={{ background: '#06b6d4' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        isConnectable={isConnectable}
        style={{ background: '#06b6d4' }}
      />
    </Card>
  );
}; 