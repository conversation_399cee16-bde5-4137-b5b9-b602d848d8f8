import React from 'react';
import { Link, useLocation } from 'wouter';
import { 
  Cpu, 
  History, 
  BarChart2, 
  Key, 
  Settings
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface SidebarProps {
  onCloseMobile?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onCloseMobile }) => {
  const isMobile = useIsMobile();
  const [location] = useLocation();

  const isActiveRoute = (route: string) => {
    if (route === '/workflows' && location === '/') {
      return true;
    }
    return location.startsWith(route);
  };

  const navItemClass = (route: string) => 
    `flex items-center px-4 py-2 ${isActiveRoute(route) 
      ? 'text-primary font-medium bg-blue-50 dark:bg-blue-900/20' 
      : 'text-neutral-600 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800'}`;

  // Handle link clicks on mobile
  const handleLinkClick = () => {
    if (isMobile && onCloseMobile) {
      onCloseMobile();
    }
  };

  return (
    <div className={`w-64 h-full bg-white dark:bg-neutral-800 border-r border-neutral-200 dark:border-neutral-700 flex flex-col ${isMobile ? 'shadow-xl' : ''}`}>
      {/* App Logo */}
      <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center text-white">
            <Cpu size={18} />
          </div>
          <h1 className="ml-3 font-semibold text-lg text-neutral-800 dark:text-white">AI Workflow</h1>
        </div>
      </div>
      
      {/* Main Menu */}
      <nav className="flex-1 py-4">
        <ul className="space-y-1">
          <li>
            <Link href="/workflows">
              <a className={navItemClass('/workflows')} onClick={handleLinkClick}>
                <Cpu className="w-5 h-5 mr-3" />
                <span>Workflows</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/run-history">
              <a className={navItemClass('/run-history')} onClick={handleLinkClick}>
                <History className="w-5 h-5 mr-3" />
                <span>Run History</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/analytics">
              <a className={navItemClass('/analytics')} onClick={handleLinkClick}>
                <BarChart2 className="w-5 h-5 mr-3" />
                <span>Analytics</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/credentials">
              <a className={navItemClass('/credentials')} onClick={handleLinkClick}>
                <Key className="w-5 h-5 mr-3" />
                <span>Credentials</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/settings">
              <a className={navItemClass('/settings')} onClick={handleLinkClick}>
                <Settings className="w-5 h-5 mr-3" />
                <span>Settings</span>
              </a>
            </Link>
          </li>
        </ul>
      </nav>
      
      {/* User Info */}
      <div className="p-4 border-t border-neutral-200 dark:border-neutral-700 flex items-center">
        <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white">
          <span className="font-medium">JS</span>
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-neutral-800 dark:text-white">Developer</p>
          <p className="text-xs text-neutral-500 dark:text-neutral-400"><EMAIL></p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
