import { Router } from 'express';
import { storage } from '../storage';
import { EnhancedCustomNodeExecutor } from '../enhanced-custom-executor';
import { z } from 'zod';

const router = Router();

// Schema for test execution request
const TestExecutionSchema = z.object({
  code: z.string(),
  input: z.any(),
  executionSettings: z.object({
    timeout: z.number().optional(),
    retryAttempts: z.number().optional(),
    enableLogging: z.boolean().optional(),
  }).optional(),
  environment: z.object({
    variables: z.record(z.string()).optional(),
    allowedDomains: z.array(z.string()).optional(),
  }).optional(),
});

// Schema for template creation
const TemplateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  category: z.string().min(1).max(100),
  code: z.string().min(1),
  inputSchema: z.array(z.any()).optional(),
  outputSchema: z.array(z.any()).optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
});

// Test custom node code execution
router.post('/test', async (req, res) => {
  try {
    const { code, input, executionSettings, environment } = TestExecutionSchema.parse(req.body);

    // Create a mock execution context for testing
    const mockContext = {
      workflow: { id: 0 },
      workflowRun: { id: 0 },
      nodeOutputs: new Map(),
      logger: {
        info: async (message: string, data?: any) => console.log('INFO:', message, data),
        warn: async (message: string, data?: any) => console.warn('WARN:', message, data),
        error: async (message: string, error: Error, data?: any) => console.error('ERROR:', message, error, data),
        debug: async (message: string, data?: any) => console.debug('DEBUG:', message, data),
      }
    };

    const nodeData = {
      code,
      executionSettings: executionSettings || {},
      environment: environment || {},
    };

    const executor = new EnhancedCustomNodeExecutor();
    const startTime = Date.now();

    try {
      const result = await executor.execute(nodeData, input, mockContext, 'test-node');
      const executionTime = Date.now() - startTime;

      res.json({
        success: true,
        result,
        executionTime,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      const executionTime = Date.now() - startTime;

      res.json({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    res.status(400).json({
      success: false,
      error: 'Invalid request data',
      details: error instanceof Error ? error.message : String(error),
    });
  }
});

// Get all custom node templates
router.get('/templates', async (req, res) => {
  try {
    const { category } = req.query;

    const templates = await storage.getCustomNodeTemplates(category as string);

    res.json({
      success: true,
      templates,
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch templates',
    });
  }
});

// Get a specific template by ID
router.get('/templates/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const template = await storage.getCustomNodeTemplate(parseInt(id));

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
      });
    }

    res.json({
      success: true,
      template,
    });
  } catch (error) {
    console.error('Error fetching template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch template',
    });
  }
});

// Create a new template
router.post('/templates', async (req, res) => {
  try {
    const templateData = TemplateSchema.parse(req.body);

    // In a real application, you would get the user ID from the session/auth
    const userId = (req as any).user?.id || null;

    const template = await storage.createCustomNodeTemplate({
      name: templateData.name,
      description: templateData.description || '',
      category: templateData.category,
      code: templateData.code,
      inputSchema: templateData.inputSchema || [],
      outputSchema: templateData.outputSchema || [],
      tags: templateData.tags || [],
      isPublic: templateData.isPublic || false,
      createdBy: userId,
    });

    res.status(201).json({
      success: true,
      template,
    });
  } catch (error) {
    console.error('Error creating template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid template data',
        details: error.errors,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create template',
    });
  }
});

// Update a template
router.put('/templates/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const templateData = TemplateSchema.partial().parse(req.body);

    // In a real application, you would check if the user owns this template
    const template = await storage.updateCustomNodeTemplate(parseInt(id), templateData);

    if (!template) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
      });
    }

    res.json({
      success: true,
      template,
    });
  } catch (error) {
    console.error('Error updating template:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid template data',
        details: error.errors,
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to update template',
    });
  }
});

// Delete a template
router.delete('/templates/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // In a real application, you would check if the user owns this template
    const success = await storage.deleteCustomNodeTemplate(parseInt(id));

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Template not found',
      });
    }

    res.json({
      success: true,
      message: 'Template deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting template:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete template',
    });
  }
});

// Get execution metrics for a node
router.get('/metrics/:nodeId', async (req, res) => {
  try {
    const { nodeId } = req.params;
    const { limit = 100 } = req.query;

    const metrics = await storage.getNodeExecutionMetrics(nodeId, parseInt(limit as string));
    const statistics = await storage.getNodeExecutionStatistics(nodeId);

    res.json({
      success: true,
      metrics,
      statistics,
    });
  } catch (error) {
    console.error('Error fetching metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch metrics',
    });
  }
});

export default router;
