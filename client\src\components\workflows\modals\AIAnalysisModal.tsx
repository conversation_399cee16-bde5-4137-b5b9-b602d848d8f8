import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Loader2, AlertTriangle, CheckCircle, Info, Lightbulb, X } from 'lucide-react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface AIAnalysisModalProps {
  isOpen: boolean;
  onClose: () => void;
  workflowId: number;
}

interface AnalysisIssue {
  severity: 'critical' | 'warning' | 'info';
  category: 'configuration' | 'connectivity' | 'performance' | 'security' | 'best_practices';
  node_id?: string;
  title: string;
  description: string;
  suggestion: string;
}

interface AnalysisSuggestion {
  category: 'optimization' | 'enhancement' | 'best_practices';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
}

interface AnalysisResult {
  overall_health: 'excellent' | 'good' | 'fair' | 'poor';
  issues: AnalysisIssue[];
  suggestions: AnalysisSuggestion[];
  summary: string;
}

const AIAnalysisModal: React.FC<AIAnalysisModalProps> = ({ isOpen, onClose, workflowId }) => {
  const { toast } = useToast();
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);

  // Get credentials for the analysis
  const { data: credentials } = useQuery<any[]>({
    queryKey: ['/api/credentials'],
    enabled: isOpen,
  });

  // Get AI analysis settings from localStorage
  const getAnalysisSettings = () => {
    const settings = localStorage.getItem('aiAnalysisSettings');
    if (settings) {
      return JSON.parse(settings);
    }
    return {
      provider: 'google',
      model: 'Gemini 2.0 Flash',
      credentialId: credentials?.[0]?.id || '',
      enabled: true
    };
  };

  const analyzeWorkflowMutation = useMutation({
    mutationFn: async () => {
      const settings = getAnalysisSettings();

      if (!settings.enabled) {
        throw new Error('AI Analysis is disabled in settings');
      }

      if (!settings.credentialId) {
        throw new Error('No credential selected for AI analysis');
      }

      return apiRequest('POST', `/api/workflows/${workflowId}/analyze`, {
        provider: settings.provider,
        model: settings.model,
        credentialId: parseInt(settings.credentialId)
      });
    },
    onSuccess: (data) => {
      setAnalysisResult(data.analysis);
      toast({
        title: "Analysis Complete",
        description: "AI analysis has been completed successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Analysis Failed",
        description: error.message || "Failed to analyze workflow",
        variant: "destructive",
      });
    }
  });

  const handleAnalyze = () => {
    setAnalysisResult(null);
    analyzeWorkflowMutation.mutate();
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'good':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'poor':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'low':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5" />
            AI Workflow Analysis
          </DialogTitle>
          <DialogDescription>
            Get AI-powered insights and suggestions for your workflow
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {!analysisResult && !analyzeWorkflowMutation.isPending && (
            <div className="flex flex-col items-center justify-center py-8 space-y-4">
              <Lightbulb className="w-16 h-16 text-muted-foreground" />
              <h3 className="text-lg font-medium">Ready to Analyze</h3>
              <p className="text-sm text-muted-foreground text-center max-w-md">
                Click the button below to start AI analysis of your workflow.
                The AI will examine your workflow structure, configurations, and connections to provide insights and suggestions.
              </p>
              <Button onClick={handleAnalyze} className="mt-4">
                <Lightbulb className="w-4 h-4 mr-2" />
                Start Analysis
              </Button>
            </div>
          )}

          {analyzeWorkflowMutation.isPending && (
            <div className="flex flex-col items-center justify-center py-8 space-y-4">
              <Loader2 className="w-8 h-8 animate-spin" />
              <h3 className="text-lg font-medium">Analyzing Workflow...</h3>
              <p className="text-sm text-muted-foreground">
                AI is examining your workflow structure and configurations
              </p>
            </div>
          )}

          {analysisResult && (
            <div className="flex-1 overflow-y-auto pr-4">
              <div className="space-y-6">
                {/* Overall Health */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5" />
                      Overall Health
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-2 mb-4">
                      <Badge className={getHealthColor(analysisResult.overall_health)}>
                        {analysisResult.overall_health.charAt(0).toUpperCase() + analysisResult.overall_health.slice(1)}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{analysisResult.summary}</p>
                  </CardContent>
                </Card>

                {/* Issues */}
                {analysisResult.issues.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5" />
                        Issues Found ({analysisResult.issues.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {analysisResult.issues.map((issue, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            {getSeverityIcon(issue.severity)}
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-medium">{issue.title}</h4>
                                <Badge className={getSeverityColor(issue.severity)}>
                                  {issue.severity}
                                </Badge>
                                <Badge variant="outline">{issue.category}</Badge>
                                {issue.node_id && (
                                  <Badge variant="secondary">Node: {issue.node_id}</Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">{issue.description}</p>
                              <div className="bg-muted p-3 rounded">
                                <p className="text-sm"><strong>Suggestion:</strong> {issue.suggestion}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {/* Suggestions */}
                {analysisResult.suggestions.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Lightbulb className="w-5 h-5" />
                        Improvement Suggestions ({analysisResult.suggestions.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {analysisResult.suggestions.map((suggestion, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            <Lightbulb className="w-4 h-4 text-blue-500 mt-0.5" />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-medium">{suggestion.title}</h4>
                                <Badge className={getImpactColor(suggestion.impact)}>
                                  {suggestion.impact} impact
                                </Badge>
                                <Badge variant="outline">{suggestion.category}</Badge>
                              </div>
                              <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}

                {analysisResult.issues.length === 0 && analysisResult.suggestions.length === 0 && (
                  <Card>
                    <CardContent className="py-8 text-center">
                      <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">Great Job!</h3>
                      <p className="text-muted-foreground">
                        No issues or suggestions found. Your workflow looks well-configured!
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <div>
              {analysisResult && (
                <Button variant="outline" onClick={handleAnalyze} disabled={analyzeWorkflowMutation.isPending}>
                  {analyzeWorkflowMutation.isPending ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Lightbulb className="w-4 h-4 mr-2" />
                  )}
                  Re-analyze
                </Button>
              )}
            </div>
            <Button variant="outline" onClick={onClose}>
              <X className="w-4 h-4 mr-2" />
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AIAnalysisModal;
