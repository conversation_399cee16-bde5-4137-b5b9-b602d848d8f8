import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import {
  insertWorkflowSchema,
  insertCredentialSchema,
  insertWorkflowRunSchema,
  insertNodeRunSchema,
  insertLogEntrySchema
} from "@shared/schema";
import { WorkflowExecutor } from "./workflow-executor";
import { rateLimit } from 'express-rate-limit';
import { validate } from 'jsonschema';

interface ApiError {
  error: string;
  details?: unknown;
  code?: string;
}

interface ApiTriggerNodeData {
  type: 'api-trigger';
  data: {
    name: string;
    description?: string;
    allowedMethods: ('GET' | 'POST' | 'PUT' | 'DELETE')[];
    authType?: 'none' | 'apiKey' | 'bearer';
    apiKeyHeader?: string;
    credentialId?: number | null;
    rateLimit?: {
      enabled: boolean;
      requestsPerMinute: number;
    };
    requestValidation?: {
      enabled: boolean;
      schema: Record<string, any>;
    };
    responseConfig?: {
      successStatus: number;
      successMessage: string;
      errorStatus: number;
      errorMessage: string;
    };
  };
}

// Utility functions
function createErrorResponse(error: unknown, defaultMessage: string): ApiError {
  if (error instanceof z.ZodError) {
    return {
      error: 'Validation Error',
      details: error.errors,
      code: 'VALIDATION_ERROR'
    };
  }
  if (error instanceof Error && error.message.includes('Rate limit exceeded')) {
    return {
      error: 'Rate Limit Exceeded',
      code: 'RATE_LIMIT_ERROR'
    };
  }
  return {
    error: error instanceof Error ? error.message : defaultMessage,
    code: 'INTERNAL_ERROR'
  };
}

function getRateLimiter(nodeId: string, requestsPerMinute: number) {
  return rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: requestsPerMinute,
    message: 'Rate limit exceeded'
  });
}

function getDataToValidate(req: Request): any {
  const method = req.method?.toUpperCase();

  switch (method) {
    case 'GET':
      return req.query || {};
    case 'POST':
    case 'PUT':
    case 'PATCH':
      return req.body || req.query || {};
    case 'DELETE':
      return req.body || req.query || {};
    default:
      return {
        method: req.method,
        headers: req.headers,
        query: req.query,
        body: req.body,
        params: req.params
      };
  }
}

function sanitizeInputData(req: Request): any {
  return {
    method: req.method,
    headers: sanitizeHeaders(req.headers),
    query: sanitizeQueryParams(req.query),
    body: sanitizeBody(req.body),
    params: sanitizeParams(req.params)
  };
}

function sanitizeHeaders(headers: any): Record<string, string> {
  const sanitized: Record<string, string> = {};
  for (const [key, value] of Object.entries(headers)) {
    if (typeof value === 'string') {
      sanitized[key.toLowerCase()] = value;
    }
  }
  return sanitized;
}

function sanitizeQueryParams(query: any): Record<string, string> {
  const sanitized: Record<string, string> = {};
  for (const [key, value] of Object.entries(query)) {
    if (typeof value === 'string') {
      sanitized[key] = value;
    }
  }
  return sanitized;
}

function sanitizeBody(body: any): any {
  if (typeof body !== 'object' || body === null) {
    return body;
  }
  return JSON.parse(JSON.stringify(body));
}

function sanitizeParams(params: any): Record<string, string> {
  const sanitized: Record<string, string> = {};
  for (const [key, value] of Object.entries(params)) {
    if (typeof value === 'string') {
      sanitized[key] = value;
    }
  }
  return sanitized;
}

function createWorkflowAnalysisPrompt(workflow: any): string {
  const nodeCount = Object.keys(workflow.nodes || {}).length;
  const edgeCount = Object.keys(workflow.edges || {}).length;

  // Analyze node types and configurations
  const nodeTypes = Object.values(workflow.nodes || {}).map((node: any) => node.type);
  const nodeTypeCount = nodeTypes.reduce((acc: any, type: string) => {
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // Check for potential issues
  const hasInputNode = nodeTypes.includes('input');
  const hasApiTrigger = nodeTypes.includes('api-trigger');
  const hasOutput = nodeTypes.some(type => ['prompt', 'agent', 'api', 'custom'].includes(type));

  // Analyze connections
  const nodeConnections = new Map();
  Object.values(workflow.edges || {}).forEach((edge: any) => {
    if (!nodeConnections.has(edge.source)) {
      nodeConnections.set(edge.source, { outgoing: 0, incoming: 0 });
    }
    if (!nodeConnections.has(edge.target)) {
      nodeConnections.set(edge.target, { outgoing: 0, incoming: 0 });
    }
    nodeConnections.get(edge.source).outgoing++;
    nodeConnections.get(edge.target).incoming++;
  });

  // Find isolated nodes
  const isolatedNodes = Object.keys(workflow.nodes || {}).filter(nodeId => {
    const connections = nodeConnections.get(nodeId);
    return !connections || (connections.incoming === 0 && connections.outgoing === 0);
  });

  return `Please analyze this workflow configuration:

**Workflow Overview:**
- Name: ${workflow.name}
- Status: ${workflow.status}
- Total Nodes: ${nodeCount}
- Total Connections: ${edgeCount}
- Node Types: ${JSON.stringify(nodeTypeCount)}

**Workflow Structure:**
- Has Input Node: ${hasInputNode}
- Has API Trigger: ${hasApiTrigger}
- Has Output Nodes: ${hasOutput}
- Isolated Nodes: ${isolatedNodes.length > 0 ? isolatedNodes.join(', ') : 'None'}

**Node Details:**
${Object.entries(workflow.nodes || {}).map(([nodeId, node]: [string, any]) => {
    const connections = nodeConnections.get(nodeId) || { incoming: 0, outgoing: 0 };
    return `- ${nodeId} (${node.type}): ${node.data?.name || 'Unnamed'} | Incoming: ${connections.incoming}, Outgoing: ${connections.outgoing}
    ${node.type === 'prompt' || node.type === 'agent' ? `Provider: ${node.data?.provider || 'Not set'}, Model: ${node.data?.model || 'Not set'}` : ''}
    ${node.type === 'api' ? `Method: ${node.data?.method || 'Not set'}, URL: ${node.data?.url || 'Not set'}` : ''}
    ${node.type === 'api-trigger' ? `Methods: ${node.data?.allowedMethods?.join(', ') || 'Not set'}` : ''}`;
  }).join('\n')}

**Edge Details:**
${Object.entries(workflow.edges || {}).map(([, edge]: [string, any]) =>
    `- ${edge.source} → ${edge.target}`
  ).join('\n')}

Please analyze this workflow for:
1. **Configuration Issues**: Missing required fields, invalid settings, credential problems
2. **Connectivity Issues**: Broken connections, isolated nodes, circular dependencies
3. **Performance Issues**: Inefficient node arrangements, potential bottlenecks
4. **Security Issues**: Exposed credentials, insecure configurations
5. **Best Practices**: Naming conventions, workflow organization, error handling

Focus on actionable insights that can help improve the workflow's reliability, performance, and maintainability.`;
}

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes prefix
  const apiPrefix = "/api";

  // Workflows
  app.get(`${apiPrefix}/workflows`, async (req: Request, res: Response) => {
    try {
      const favorites = req.query.favorites === 'true';
      const workflows = favorites
        ? await storage.getFavoriteWorkflows()
        : await storage.getWorkflows();
      res.json(workflows);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflows" });
    }
  });

  app.get(`${apiPrefix}/workflows/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const workflow = await storage.getWorkflow(id);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json(workflow);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow" });
    }
  });

  app.post(`${apiPrefix}/workflows`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertWorkflowSchema.parse(req.body);
      const workflow = await storage.createWorkflow(validatedData);
      res.status(201).json(workflow);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create workflow" });
    }
  });

  app.patch(`${apiPrefix}/workflows/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertWorkflowSchema.partial().parse(req.body);
      const workflow = await storage.updateWorkflow(id, validatedData);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json(workflow);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to update workflow" });
    }
  });

  app.delete(`${apiPrefix}/workflows/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ error: "Invalid workflow ID" });
      }

      const success = await storage.deleteWorkflow(id);
      if (!success) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json({ success: true });
    } catch (error) {
      console.error('Error in delete workflow endpoint:', error);
      res.status(500).json({
        error: "Failed to delete workflow",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.post(`${apiPrefix}/workflows/:id/favorite`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const workflow = await storage.toggleWorkflowFavorite(id);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json(workflow);
    } catch (error) {
      res.status(500).json({ error: "Failed to toggle workflow favorite status" });
    }
  });

  // Credentials
  app.get(`${apiPrefix}/credentials`, async (_req: Request, res: Response) => {
    try {
      const credentials = await storage.getCredentials();
      res.json(credentials);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch credentials" });
    }
  });

  app.get(`${apiPrefix}/credentials/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const credential = await storage.getCredential(id);
      if (!credential) {
        return res.status(404).json({ error: "Credential not found" });
      }

      // Mask API key for security
      const maskedCredential = {
        ...credential,
        apiKey: credential.apiKey.substring(0, 4) + "..." + credential.apiKey.substring(credential.apiKey.length - 4)
      };

      res.json(maskedCredential);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch credential" });
    }
  });

  app.post(`${apiPrefix}/credentials`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertCredentialSchema.parse(req.body);
      const credential = await storage.createCredential(validatedData);
      res.status(201).json(credential);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create credential" });
    }
  });

  app.patch(`${apiPrefix}/credentials/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertCredentialSchema.partial().parse(req.body);
      const credential = await storage.updateCredential(id, validatedData);
      if (!credential) {
        return res.status(404).json({ error: "Credential not found" });
      }
      res.json(credential);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to update credential" });
    }
  });

  app.delete(`${apiPrefix}/credentials/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteCredential(id);
      if (!success) {
        return res.status(404).json({ error: "Credential not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete credential" });
    }
  });

  // Workflow Runs
  app.get(`${apiPrefix}/workflow-runs`, async (req: Request, res: Response) => {
    try {
      const workflowId = req.query.workflowId ? parseInt(req.query.workflowId as string) : undefined;
      const runs = await storage.getWorkflowRuns(workflowId);
      res.json(runs);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow runs" });
    }
  });

  app.get(`${apiPrefix}/workflow-runs/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const run = await storage.getWorkflowRun(id);
      if (!run) {
        return res.status(404).json({ error: "Workflow run not found" });
      }
      res.json(run);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow run" });
    }
  });

  app.post(`${apiPrefix}/workflow-runs`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertWorkflowRunSchema.parse(req.body);
      const run = await storage.createWorkflowRun(validatedData);

      // Start workflow execution asynchronously
      const workflowExecutor = new WorkflowExecutor();
      workflowExecutor.executeWorkflow(run.id).catch((error: any) => {
        console.error(`Workflow execution failed for run ${run.id}:`, error);
      });

      res.status(201).json(run);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create workflow run" });
    }
  });

  app.patch(`${apiPrefix}/workflow-runs/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const run = await storage.updateWorkflowRun(id, req.body);
      if (!run) {
        return res.status(404).json({ error: "Workflow run not found" });
      }
      res.json(run);
    } catch (error) {
      res.status(500).json({ error: "Failed to update workflow run" });
    }
  });

  // Node Runs
  app.get(`${apiPrefix}/workflow-runs/:runId/node-runs`, async (req: Request, res: Response) => {
    try {
      const runId = parseInt(req.params.runId);
      const nodeRuns = await storage.getNodeRuns(runId);
      res.json(nodeRuns);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch node runs" });
    }
  });

  app.post(`${apiPrefix}/node-runs`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertNodeRunSchema.parse(req.body);
      const nodeRun = await storage.createNodeRun(validatedData);
      res.status(201).json(nodeRun);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create node run" });
    }
  });

  app.patch(`${apiPrefix}/node-runs/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const nodeRun = await storage.updateNodeRun(id, req.body);
      if (!nodeRun) {
        return res.status(404).json({ error: "Node run not found" });
      }
      res.json(nodeRun);
    } catch (error) {
      res.status(500).json({ error: "Failed to update node run" });
    }
  });

  // Log entry routes
  app.get(`${apiPrefix}/workflow-runs/:workflowRunId/logs`, async (req: Request, res: Response) => {
    try {
      const workflowRunId = parseInt(req.params.workflowRunId);
      const nodeRunId = req.query.nodeRunId ? parseInt(req.query.nodeRunId as string) : undefined;
      const level = req.query.level as string;

      let logs;
      if (level) {
        logs = await storage.getLogEntriesByLevel(workflowRunId, level);
      } else {
        logs = await storage.getLogEntries(workflowRunId, nodeRunId);
      }

      res.json(logs);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch log entries" });
    }
  });

  app.get(`${apiPrefix}/node-runs/:nodeRunId/logs`, async (req: Request, res: Response) => {
    try {
      const nodeRunId = parseInt(req.params.nodeRunId);

      // Get the node run to find the workflow run ID
      const nodeRun = await storage.getNodeRun(nodeRunId);

      if (!nodeRun) {
        return res.status(404).json({ error: "Node run not found" });
      }

      const logs = await storage.getLogEntries(nodeRun.workflowRunId, nodeRunId);
      res.json(logs);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch node run logs" });
    }
  });

  // Replace the API trigger endpoint implementation
  app.all('/api/trigger/:workflowId/:nodeId', async (req: Request, res: Response) => {
    let node: ApiTriggerNodeData | undefined;
    try {
      const { workflowId, nodeId } = req.params;

      // Get the workflow
      const workflow = await storage.getWorkflow(parseInt(workflowId));
      if (!workflow) {
        return res.status(404).json(createErrorResponse(null, 'Workflow not found'));
      }

      // Find the API trigger node
      const nodes = workflow.nodes as Record<string, ApiTriggerNodeData>;
      node = nodes[nodeId];
      if (!node || node.type !== 'api-trigger') {
        return res.status(404).json(createErrorResponse(null, 'API trigger node not found'));
      }

      // Check if the HTTP method is allowed
      const allowedMethods = node.data.allowedMethods || ['GET', 'POST', 'PUT', 'DELETE'];
      if (!allowedMethods.includes(req.method as 'GET' | 'POST' | 'PUT' | 'DELETE')) {
        return res.status(405).json(createErrorResponse(null,
          `Method ${req.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`
        ));
      }

      // Apply rate limiting at the route level if enabled
      if (node.data.rateLimit?.enabled) {
        const limiter = getRateLimiter(nodeId, node.data.rateLimit.requestsPerMinute);
        await new Promise((resolve, reject) => {
          limiter(req, res, (err) => {
            if (err) reject(err);
            else resolve(undefined);
          });
        });
      }

      // Validate request if validation is enabled
      if (node.data.requestValidation?.enabled) {
        const dataToValidate = getDataToValidate(req);
        const validation = validate(dataToValidate, node.data.requestValidation.schema);
        if (!validation.valid) {
          return res.status(400).json(createErrorResponse(validation, 'Request validation failed'));
        }
      }

      // Check API key authentication if required
      if (node.data.authType === 'apiKey') {
        const apiKeyHeader = node.data.apiKeyHeader || 'x-api-key';
        const providedKey = req.headers[apiKeyHeader.toLowerCase()];

        if (node.data.credentialId) {
          const credential = await storage.getCredential(node.data.credentialId);
          if (!credential || providedKey !== credential.apiKey) {
            return res.status(401).json(createErrorResponse(null, 'Invalid API key'));
          }
        }
      }

      // Sanitize and prepare input data
      const sanitizedInput = sanitizeInputData(req);

      // Create workflow run with API trigger
      const workflowRun = await storage.createWorkflowRun({
        workflowId: parseInt(workflowId),
        status: 'pending',
        triggerType: 'api',
        input: sanitizedInput
      });

      // Start workflow execution asynchronously
      const workflowExecutor = new WorkflowExecutor();
      workflowExecutor.executeWorkflow(workflowRun.id).catch((error: any) => {
        console.error(`API triggered workflow execution failed for run ${workflowRun.id}:`, error);
      });

      // Use configured response status codes and messages
      const successStatus = node.data.responseConfig?.successStatus || 200;
      const successMessage = node.data.responseConfig?.successMessage || 'Workflow triggered successfully';

      res.status(successStatus).json({
        success: true,
        runId: workflowRun.id,
        message: successMessage
      });
    } catch (error) {
      console.error('API trigger error:', error);

      const statusCode = node?.data?.responseConfig?.errorStatus || 500;
      const errorMessage = node?.data?.responseConfig?.errorMessage || 'Failed to trigger workflow';

      res.status(statusCode).json(createErrorResponse(error, errorMessage));
    }
  });

  // Workflow Analysis Route
  app.post(`${apiPrefix}/workflows/:id/analyze`, async (req: Request, res: Response) => {
    try {
      const workflowId = parseInt(req.params.id);
      const { provider, model, credentialId } = req.body;

      if (isNaN(workflowId)) {
        return res.status(400).json({ error: "Invalid workflow ID" });
      }

      // Get the workflow
      const workflow = await storage.getWorkflow(workflowId);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }

      // Get the credential for AI analysis
      const credential = await storage.getCredential(credentialId);
      if (!credential) {
        return res.status(400).json({ error: "Invalid credential ID" });
      }

      // Import AI service
      const { AIService } = await import('./ai-service');
      const aiService = new AIService();

      // Create analysis prompt
      const analysisPrompt = createWorkflowAnalysisPrompt(workflow);

      const aiRequest = {
        provider: provider || 'google',
        model: model || 'Gemini 2.0 Flash',
        prompt: analysisPrompt,
        systemPrompt: `You are an expert workflow analyzer. Analyze the provided workflow configuration and identify potential issues, errors, and areas for improvement. Provide specific, actionable suggestions for fixes.`,
        maxTokens: 2000,
        temperature: 0.3,
        outputFormat: 'json' as const,
        schema: {
          type: 'object',
          properties: {
            overall_health: {
              type: 'string',
              enum: ['excellent', 'good', 'fair', 'poor'],
              description: 'Overall workflow health assessment'
            },
            issues: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  severity: { type: 'string', enum: ['critical', 'warning', 'info'] },
                  category: { type: 'string', enum: ['configuration', 'connectivity', 'performance', 'security', 'best_practices'] },
                  node_id: { type: 'string', description: 'ID of the affected node, if applicable' },
                  title: { type: 'string', description: 'Brief title of the issue' },
                  description: { type: 'string', description: 'Detailed description of the issue' },
                  suggestion: { type: 'string', description: 'Specific suggestion to fix the issue' }
                },
                required: ['severity', 'category', 'title', 'description', 'suggestion']
              }
            },
            suggestions: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  category: { type: 'string', enum: ['optimization', 'enhancement', 'best_practices'] },
                  title: { type: 'string', description: 'Brief title of the suggestion' },
                  description: { type: 'string', description: 'Detailed description of the suggestion' },
                  impact: { type: 'string', enum: ['high', 'medium', 'low'], description: 'Expected impact of implementing this suggestion' }
                },
                required: ['category', 'title', 'description', 'impact']
              }
            },
            summary: { type: 'string', description: 'Brief summary of the analysis' }
          },
          required: ['overall_health', 'issues', 'suggestions', 'summary']
        }
      };

      const result = await aiService.generateResponse(aiRequest, credential);

      res.json({
        success: true,
        analysis: result.content,
        workflow_name: workflow.name,
        analyzed_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('Workflow analysis error:', error);
      res.status(500).json({
        error: "Failed to analyze workflow",
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Custom Node Routes
  const customNodeRoutes = await import('./routes/custom-nodes');
  app.use(`${apiPrefix}/custom-nodes`, customNodeRoutes.default);

  const httpServer = createServer(app);
  return httpServer;
}
